import asyncio
import os
from tortoise import Tortoise


async def init_db():
    # 数据库连接配置
    DB_URL = os.getenv("DB_URL", "sqlite://db.sqlite3")

    # 初始化Tortoise ORM
    await Tortoise.init(db_url=DB_URL, modules={"models": ["app.models"]})


async def update_settings():
    # 导入模型
    from app.models.strm import SystemSettings

    # 获取当前系统设置
    settings = await SystemSettings.all().first()
    if not settings:
        print("系统设置不存在，将创建一个新的配置")
        settings = SystemSettings()

    # 打印当前配置
    print(f"当前配置版本: {settings.settings_version}")
    print(f"当前视频文件类型: '{settings.video_file_types}'")
    print(f"当前音频文件类型: '{settings.audio_file_types}'")
    print(f"当前图片文件类型: '{settings.image_file_types}'")
    print(f"当前字幕文件类型: '{settings.subtitle_file_types}'")
    print(f"当前元数据文件类型: '{settings.metadata_file_types}'")

    # 更新文件类型配置
    settings.video_file_types = "mkv,mp4,avi,mov,wmv,flv,webm,m4v,mpg,mpeg"
    settings.audio_file_types = "mp3,wav,flac,aac,ogg,wma,m4a"
    settings.image_file_types = "jpg,jpeg,png,gif,bmp,webp,tiff"
    settings.subtitle_file_types = "srt,ass,ssa,vtt,sub"
    settings.metadata_file_types = "nfo,xml,json"

    # 更新版本号
    settings.settings_version = settings.settings_version + 1

    # 保存更新
    await settings.save()

    print("\n更新后的配置:")
    print(f"配置版本: {settings.settings_version}")
    print(f"视频文件类型: '{settings.video_file_types}'")
    print(f"音频文件类型: '{settings.audio_file_types}'")
    print(f"图片文件类型: '{settings.image_file_types}'")
    print(f"字幕文件类型: '{settings.subtitle_file_types}'")
    print(f"元数据文件类型: '{settings.metadata_file_types}'")

    print("\n配置更新完成！请重新创建STRM任务来测试。")


async def main():
    print("开始更新STRM系统设置...")
    await init_db()
    await update_settings()
    await Tortoise.close_connections()
    print("更新完成！")


if __name__ == "__main__":
    asyncio.run(main())
