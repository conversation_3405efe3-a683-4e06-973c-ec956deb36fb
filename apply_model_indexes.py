#!/usr/bin/env python
"""
应用模型中定义的索引
重新生成数据库schema以包含新的索引定义
"""

import asyncio
import sys
from tortoise import Tortoise
from app.settings.config import settings


async def apply_model_indexes():
    """应用模型中定义的索引"""
    print("开始应用模型索引...")

    # 连接到数据库
    await Tortoise.init(
        config=settings.TORTOISE_ORM
    )

    try:
        # 重新生成schema以包含新的索引
        print("重新生成数据库schema...")
        await Tortoise.generate_schemas()
        
        # 验证索引是否创建成功
        conn = Tortoise.get_connection("conn_system")
        result = await conn.execute_query(
            "SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%' ORDER BY name"
        )
        
        if result[1]:
            print(f"✅ 成功应用索引，当前共有 {len(result[1])} 个自定义索引:")
            for row in result[1]:
                print(f"  - {row[0]}")
        else:
            print("⚠️ 未找到自定义索引，可能需要手动创建")
            
        print("\n📊 模型索引应用完成!")
        
    except Exception as e:
        print(f"❌ 应用模型索引失败: {str(e)}")
        raise
    finally:
        # 关闭连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    try:
        asyncio.run(apply_model_indexes())
        sys.exit(0)
    except Exception as e:
        print(f"应用索引失败: {str(e)}")
        sys.exit(1)
