# 任务详情页面统计信息增强

## 修改内容

在任务详情页面的"文件处理统计"部分添加了STRM文件数和资源文件数的显示。

### 修改前
原来只显示4个统计项：
- 总文件数
- 已处理文件
- 成功文件数
- 失败文件数

### 修改后
现在显示6个统计项：
- 总文件数
- 已处理文件
- 成功文件数
- 失败文件数
- **STRM文件数** (新增)
- **资源文件数** (新增)

## 技术实现

### 1. 布局调整
- 将原来的4列网格布局改为6列
- 添加了响应式CSS样式，确保在不同屏幕尺寸下都能正常显示：
  - 小屏幕（<768px）：2列布局
  - 中等屏幕（768px-1023px）：3列布局
  - 大屏幕（≥1024px）：6列布局

### 2. 新增统计项
- **STRM文件数**：
  - 图标：`mdi:file-video`（蓝色）
  - 数据字段：`currentTask.strm_files_count`
  - 工具提示：生成的STRM流媒体文件数量，用于媒体服务器播放

- **资源文件数**：
  - 图标：`mdi:file-download`（橙色）
  - 数据字段：`currentTask.resource_files_count`
  - 工具提示：下载的资源文件数量，包括字幕、海报等附加文件

### 3. 用户体验优化
- 为新增的统计项添加了工具提示，鼠标悬停时显示详细说明
- 使用不同颜色的图标来区分不同类型的文件
- 保持了与现有统计项一致的视觉风格

## 修改的文件

### `web/src/views/strm/tasks/index.vue`

#### 1. 模板部分（第130-200行）
```vue
<n-grid :cols="6" :x-gap="16" class="stat-grid">
  <!-- 原有的4个统计项 -->
  
  <!-- 新增：STRM文件数 -->
  <n-gi>
    <n-tooltip trigger="hover">
      <template #trigger>
        <n-statistic label="STRM文件数">
          <div class="stat-value">
            <n-icon size="24" class="mr-1" style="color: #2080f0;">
              <Icon icon="mdi:file-video" />
            </n-icon>
            {{ currentTask.strm_files_count || 0 }}
          </div>
        </n-statistic>
      </template>
      生成的STRM流媒体文件数量，用于媒体服务器播放
    </n-tooltip>
  </n-gi>
  
  <!-- 新增：资源文件数 -->
  <n-gi>
    <n-tooltip trigger="hover">
      <template #trigger>
        <n-statistic label="资源文件数">
          <div class="stat-value">
            <n-icon size="24" class="mr-1" style="color: #f0a020;">
              <Icon icon="mdi:file-download" />
            </n-icon>
            {{ currentTask.resource_files_count || 0 }}
          </div>
        </n-statistic>
      </template>
      下载的资源文件数量，包括字幕、海报等附加文件
    </n-tooltip>
  </n-gi>
</n-grid>
```

#### 2. 导入部分（第312-336行）
```typescript
import {
  // ... 其他组件
  NTooltip,  // 新增
  useMessage
} from 'naive-ui';
```

#### 3. 样式部分（第1596-1633行）
```css
/* 统计网格响应式样式 */
.stat-grid {
  display: grid;
  gap: 16px;
}

/* 小屏幕：2列 */
@media (max-width: 767px) {
  .stat-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 中等屏幕：3列 */
@media (min-width: 768px) and (max-width: 1023px) {
  .stat-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 大屏幕：6列 */
@media (min-width: 1024px) {
  .stat-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}
```

## 数据来源

这些统计数据来自后端API返回的任务详情，字段定义如下：

- `strm_files_count`: STRM文件数量
- `resource_files_count`: 资源文件数量

这些字段已经在后端的任务状态API中返回，前端类型定义也已包含这些字段。

## 测试建议

1. 打开任务管理页面
2. 点击任意任务的"详情"按钮
3. 查看"文件处理统计"卡片
4. 验证显示了6个统计项
5. 鼠标悬停在"STRM文件数"和"资源文件数"上，验证工具提示显示
6. 在不同屏幕尺寸下测试响应式布局

## 预期效果

- 用户可以更清楚地了解任务处理的详细情况
- 区分STRM文件和资源文件的处理状态
- 在不同设备上都有良好的显示效果
- 通过工具提示提供更好的用户体验
