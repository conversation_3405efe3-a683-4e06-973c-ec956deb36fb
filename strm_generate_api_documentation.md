# `/strm/generate` 接口执行流程文档

## 流程图表

本文档包含两个流程图来直观展示/strm/generate接口的执行流程：

1. **API调用时序图** - 展示从客户端发起请求到任务完成的完整时序流程，包括各组件间的交互
2. **STRM文件生成流程图** - 详细展示文件处理的逻辑流程，包括各种决策点和处理步骤

请参考这两个流程图以直观理解接口的工作原理。

## 接口概述

`/strm/generate` 接口用于创建STRM文件生成任务，将解析后的115网盘目录树中的视频文件转换为可在媒体服务器中播放的STRM文件。STRM文件本质上是包含媒体文件URL的文本文件，可以让媒体服务器直接播放远程内容而无需本地存储完整文件。

### 接口定义

```
POST /strm/generate
```

### 请求参数

| 参数名             | 类型    | 必填 | 描述                                                       |
| ------------------ | ------- | ---- | ---------------------------------------------------------- |
| record_id          | int     | 是   | 已解析的上传记录ID                                         |
| server_id          | int     | 是   | 媒体服务器ID                                               |
| output_dir         | string  | 否   | 自定义输出目录，默认为系统设置的目录                       |
| name               | string  | 否   | 自定义任务名称                                             |
| download_server_id | int     | 否   | 下载服务器ID（用于资源文件下载，已废弃，保留用于向后兼容） |
| download_resources | boolean | 否   | 是否下载资源文件（已废弃，保留用于向后兼容）               |

### 响应结果

成功响应：

```json
{
  "code": 2000,
  "msg": "Success",
  "data": {
    "task_id": 123,
    "message": "任务已成功创建并开始处理"
  }
}
```

失败响应：

```json
{
  "code": 4004,
  "msg": "找不到ID为xxx的媒体服务器"
}
```

或

```json
{
  "code": 5000,
  "msg": "创建任务时发生严重错误，请检查后台日志: xxx"
}
```

## 执行流程详解

### 1. 接口请求处理

1. 客户端向 `/strm/generate` 接口发送POST请求，包含必要参数
2. FastAPI框架解析请求参数并验证
3. 通过依赖注入获取当前认证用户信息

### 2. 任务创建阶段

在`create_strm_task`函数中：

1. **服务器验证**：验证指定的媒体服务器是否存在
2. **输出目录确定**：
   - 如果用户提供了输出目录，使用用户指定的目录
   - 否则，从系统设置获取基础输出目录，并创建格式为`task_YYYYMMDD_HHMMSS_userId`的子目录
3. **任务名称生成**：
   - 如果用户提供了名称，使用用户指定的名称
   - 否则，生成默认名称：`STRM生成-记录{record_id}-{当前时间}`
4. **创建任务记录**：
   - 在数据库中创建`StrmTask`记录，状态为`PENDING`
   - 记录服务器ID、源文件ID、输出目录等信息
5. **额外信息保存**：
   - 如果提供了下载服务器ID或需要下载资源，将这些信息存储在任务的日志字段中（用于向后兼容）

### 3. 后台任务调度

1. 使用`BgTasks.add_job`将任务添加到后台队列：
   - 指定要执行的函数：`start_strm_task`
   - 使用'date'触发器设置为立即执行
   - 传入任务ID和用户ID作为参数
   - 设置任务标识和名称
   - 配置为替换同ID的已存在任务（防止重复）
2. 返回成功响应，包含任务ID和提示信息

### 4. 后台任务执行

在`start_strm_task`函数中：

1. **信息获取**：
   - 获取用户信息
   - 获取任务信息
2. **验证阶段**：
   - 验证用户权限（任务创建者）
   - 检查任务状态（防止重复执行）
3. **解析结果获取**：
   - 获取上传记录的解析结果
   - 过滤出视频文件
4. **空结果处理**：
   - 如果没有找到视频文件，更新任务状态为已完成，但记录为0文件
5. **任务状态更新**：
   - 更新任务总文件数
   - 将任务状态设置为`RUNNING`
   - 记录开始时间
6. **处理启动**：
   - 调用`process_strm_files`函数开始处理文件

### 5. STRM文件生成过程

在`process_strm_files`函数中：

1. **配置获取**：
   - 获取媒体服务器信息
   - 获取系统设置（文件类型定义、路径替换等）
2. **处理器初始化**：
   - 创建`StrmProcessor`实例
3. **文件处理**：
   - 调用`processor.process_files`方法处理文件列表

在`process_files`方法中：

1. **文件统计**：
   - 计算总文件数和视频文件数
   - 更新任务总文件数
2. **循环处理**：
   - 遍历文件列表，处理每个视频文件
   - 对每个视频文件，调用`generate_strm`方法
   - 更新任务进度（处理文件数、成功文件数、失败文件数）
3. **完成处理**：
   - 所有文件处理完成后，记录处理时间
   - 更新任务状态为`COMPLETED`
   - 记录任务结束时间

### 6. 单个STRM文件生成

在`generate_strm`方法中：

1. **路径处理**：
   - 获取原始文件路径
   - 应用路径替换（如启用）
2. **URL生成**：
   - 使用媒体服务器基础URL和处理后的路径生成完整URL
3. **输出目录结构**：
   - 保持原始目录结构
   - 创建必要的目录
4. **文件创建**：
   - 生成STRM文件名（原文件名.strm）
   - 创建STRM文件并写入URL内容
5. **记录生成**：
   - 如果提供了任务ID，创建`StrmFile`记录
   - 记录源路径、目标路径、文件类型、成功状态等

### 7. 异常处理机制

1. **接口层异常处理**：
   - 捕获并区分HTTP异常和普通异常
   - 对于HTTP异常，直接重新抛出
   - 对于普通异常，记录详细日志并返回通用错误响应
2. **任务执行异常处理**：
   - 捕获所有异常，确保后台任务不会意外中断
   - 尝试更新任务状态为`FAILED`
   - 记录错误详情到任务日志
3. **文件处理异常处理**：
   - 对单个文件处理失败不影响整体任务
   - 记录失败文件的错误信息
   - 更新任务的失败文件计数

## 关键组件说明

### StrmTask 数据模型

核心字段：
- id: 任务ID
- name: 任务名称
- server_id: 媒体服务器ID
- source_file: 源文件记录ID
- output_dir: 输出目录
- status: 任务状态（PENDING/RUNNING/COMPLETED/FAILED）
- total_files: 总文件数
- processed_files: 已处理文件数
- success_files: 成功文件数
- failed_files: 失败文件数
- created_by: 创建用户
- start_time: 开始时间
- end_time: 结束时间
- log_content: 日志内容

### MediaServer 数据模型

核心字段：
- id: 服务器ID
- name: 服务器名称
- base_url: 基础URL
- server_type: 服务器类型
- username: 用户名（可选）
- password: 密码（可选）

### BgTasks 后台任务系统

- 基于APScheduler的任务调度系统
- 支持多种触发器（date、interval、cron）
- 处理异步任务，避免阻塞API响应

### StrmProcessor 处理器

核心功能：
- 文件类型识别
- STRM文件生成
- 路径处理和替换
- 处理结果跟踪

## 性能考量

1. **异步处理优势**
   - API快速响应，任务在后台执行
   - 用户无需等待长时间操作完成
   - 可以并行处理多个任务

2. **潜在瓶颈**
   - 大量文件处理可能导致内存压力
   - 文件系统操作（写入STRM文件）可能受磁盘I/O限制
   - 数据库更新频繁（每处理一个文件）可能造成数据库压力

3. **优化措施**
   - 批量更新数据库状态
   - 限制同时执行的任务数量
   - 使用线程池进行文件系统操作

## 任务状态查询

用户可以通过以下接口查询任务状态：

1. `/strm/task/{task_id}` - 获取任务状态和进度
2. `/strm/task/{task_id}/logs` - 获取任务日志
3. `/strm/download-strm/{task_id}` - 下载生成的STRM文件（ZIP格式）

## 总结

`/strm/generate`接口采用异步处理方式，将STRM文件生成任务放入后台队列执行，具有以下特点：

1. **高效响应** - 接口立即返回，不阻塞用户操作
2. **可追踪性** - 任务创建后可通过ID查询状态和进度
3. **容错设计** - 单个文件失败不影响整体任务
4. **可扩展性** - 基于后台任务系统，可轻松扩展处理能力

这种设计适合处理大量文件的场景，用户可以在前端显示任务进度，也可以在任务完成后下载生成的STRM文件包。

完整的接口执行流程贯穿了多个系统组件，从Web API到后台任务系统，再到文件处理和数据库操作，体现了系统的分层设计和模块化架构。 