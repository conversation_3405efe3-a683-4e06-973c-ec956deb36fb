# This file is @generated by PDM.
# Please do not edit it manually.

aerich==0.8.1
aiosqlite==0.20.0
annotated-types==0.7.0
anyio==4.7.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
async-timeout==5.0.1; python_full_version < "3.11.3"
asyncclick==8.1.7.2
cffi==1.17.1
click==8.1.8
colorama==0.4.6; sys_platform == "win32" or platform_system == "Windows"
dictdiffer==0.9.0
dnspython==2.7.0
email-validator==2.2.0
exceptiongroup==1.2.2; python_version < "3.11"
fastapi==0.115.6
fastapi-cache2==0.2.2
h11==0.14.0
idna==3.10
iso8601==2.1.0
loguru==0.7.3
orjson==3.10.13
passlib==1.7.4
pendulum==3.0.0
pycparser==2.22
pydantic==2.10.4
pydantic-core==2.27.2
pydantic-settings==2.7.0
pyjwt==2.10.1
pypika-tortoise==0.3.2
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
pytz==2024.2
redis==5.2.1
setuptools==75.6.0
six==1.17.0
sniffio==1.3.1
starlette==0.41.3
time-machine==2.16.0; implementation_name != "pypy"
tortoise-orm==0.23.0
typing-extensions==4.12.2
tzdata==2024.2
uvicorn==0.34.0
win32-setctime==1.2.0; sys_platform == "win32"
--extra-index-url https://pypi.tuna.tsinghua.edu.cn/simple/
