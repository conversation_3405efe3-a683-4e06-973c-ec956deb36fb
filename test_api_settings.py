#!/usr/bin/env python3
"""
通过HTTP API测试系统设置修复的脚本
"""
import requests
import json

# API基础URL
BASE_URL = "http://localhost:9999/api/v1"

# 测试用的认证token（需要先登录获取）
# 这里使用一个示例token，实际使用时需要先登录
AUTH_TOKEN = None

def get_auth_token():
    """获取认证token"""
    login_data = {
        "userName": "admin",  # 替换为实际的用户名
        "password": "123456"  # 替换为实际的密码
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
        if response.status_code == 200:
            result = response.json()
            if result.get("code") == "0000":
                return result["data"]["access_token"]
        print(f"登录失败: {response.text}")
        return None
    except Exception as e:
        print(f"登录请求失败: {str(e)}")
        return None

def get_headers():
    """获取请求头"""
    if AUTH_TOKEN:
        return {
            "Authorization": f"Bearer {AUTH_TOKEN}",
            "Content-Type": "application/json"
        }
    return {"Content-Type": "application/json"}

def test_get_settings():
    """测试获取系统设置"""
    print("1. 测试获取系统设置:")
    
    try:
        response = requests.get(f"{BASE_URL}/system-manage/settings", headers=get_headers())
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应代码: {result.get('code')}")
            
            if result.get("code") == "0000":
                data = result.get("data", {})
                retry_count = data.get("failure_retry_count")
                retry_interval = data.get("retry_interval_seconds")
                
                print(f"   ✅ 获取成功")
                print(f"   失败重试次数: {retry_count}")
                print(f"   重试间隔时间: {retry_interval}")
                
                # 检查字段是否存在
                if retry_count is not None and retry_interval is not None:
                    print("   ✅ 重试配置字段存在")
                    return data
                else:
                    print("   ❌ 重试配置字段缺失")
                    return None
            else:
                print(f"   ❌ API返回错误: {result.get('msg')}")
                return None
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 请求异常: {str(e)}")
        return None

def test_update_settings():
    """测试更新系统设置"""
    print("\n2. 测试更新系统设置:")
    
    # 测试数据
    test_data = {
        "failure_retry_count": 2,
        "retry_interval_seconds": 20
    }
    
    print(f"   设置失败重试次数为: {test_data['failure_retry_count']}")
    print(f"   设置重试间隔时间为: {test_data['retry_interval_seconds']}")
    
    try:
        response = requests.post(f"{BASE_URL}/system-manage/settings", 
                               json=test_data, headers=get_headers())
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应代码: {result.get('code')}")
            
            if result.get("code") == "0000":
                print("   ✅ 更新成功")
                return True
            else:
                print(f"   ❌ API返回错误: {result.get('msg')}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔍 开始测试系统设置API修复...")
    
    # 尝试获取认证token
    global AUTH_TOKEN
    AUTH_TOKEN = get_auth_token()
    if not AUTH_TOKEN:
        print("⚠️  无法获取认证token，将尝试无认证访问")
    
    # 1. 获取当前设置
    original_settings = test_get_settings()
    if not original_settings:
        print("❌ 无法获取系统设置，测试终止")
        return
    
    # 2. 测试更新设置
    if test_update_settings():
        # 3. 重新获取设置验证
        print("\n3. 重新获取设置验证:")
        updated_settings = test_get_settings()
        
        if updated_settings:
            retry_count = updated_settings.get("failure_retry_count")
            retry_interval = updated_settings.get("retry_interval_seconds")
            
            if retry_count == 2 and retry_interval == 20:
                print("   ✅ 测试通过！设置值正确保存和获取")
            else:
                print("   ❌ 测试失败！设置值不匹配")
                print(f"   期望: 重试次数=2, 重试间隔=20")
                print(f"   实际: 重试次数={retry_count}, 重试间隔={retry_interval}")
        
        # 4. 恢复原始设置
        print("\n4. 恢复原始设置:")
        restore_data = {
            "failure_retry_count": original_settings.get("failure_retry_count", 3),
            "retry_interval_seconds": original_settings.get("retry_interval_seconds", 30)
        }
        
        try:
            response = requests.post(f"{BASE_URL}/system-manage/settings", 
                                   json=restore_data, headers=get_headers())
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == "0000":
                    print("   ✅ 原始设置已恢复")
                else:
                    print(f"   ❌ 恢复失败: {result.get('msg')}")
            else:
                print(f"   ❌ 恢复请求失败: {response.text}")
        except Exception as e:
            print(f"   ❌ 恢复请求异常: {str(e)}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
