# 任务管理详情页文件类型过滤功能实现（后端过滤版本）

## 功能概述

实现了任务管理详情页文件管理过滤条件，根据系统设置中的文件类型设置来过滤文件。过滤条件包括：
- 📹 视频文件
- 🎵 音频文件
- 🖼️ 图片文件
- 📝 字幕文件
- 📄 元数据文件

**重要改进**：采用后端过滤方式，支持大量文件的高效过滤和分页。

## 实现细节

### 1. 修改的文件
- `app/api/v1/strm.py` - 后端API接口
- `app/controllers/strm/task_controller.py` - 后端控制器
- `web/src/service/api/strm.ts` - 前端API服务
- `web/src/components/strm/TaskFileList.vue` - 前端文件列表组件
- `web/src/views/strm/tasks/index.vue` - 任务管理页面

### 2. 主要功能

#### 2.1 后端过滤支持
- **API接口增强**：`/strm/task/{task_id}/files` 支持 `file_type`、`search`、`status` 过滤参数
- **高效查询**：在数据库层面进行过滤，支持大量文件的高效处理
- **分页优化**：过滤后的结果支持真正的分页，避免前端内存压力

#### 2.2 系统设置集成
- 后端自动获取系统设置进行文件类型分类
- 根据系统设置中的 `video_file_types`、`audio_file_types`、`image_file_types`、`subtitle_file_types`、`metadata_file_types` 字段进行分类
- 支持动态配置，无需硬编码文件类型

#### 2.3 多维度过滤
- **文件类型过滤**：视频、音频、图片、字幕、元数据五种类型
- **搜索过滤**：支持文件名和路径的关键词搜索
- **状态过滤**：支持按处理成功/失败状态过滤
- **组合过滤**：支持多个过滤条件同时使用

#### 2.4 前端优化
- 移除前端过滤逻辑，减少内存占用
- 实时响应过滤条件变化
- 自动重置分页到第一页
- 支持过滤条件的清除和重置

### 3. 技术实现

#### 3.1 后端API增强
```python
@router_strm.get("/task/{task_id}/files", summary="获取任务文件列表")
async def get_task_files_endpoint(
    task_id: int = Path(..., description="任务ID"),
    current_user: User = Depends(get_current_user),
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    file_type: Optional[str] = Query(None, description="文件类型过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[bool] = Query(None, description="处理状态过滤"),
):
```

#### 3.2 后端文件类型分类
```python
def _get_file_type_category(file_path: str, settings: dict) -> str:
    """根据系统设置判断文件类型分类"""
    # 获取文件扩展名并匹配系统设置中的文件类型配置
    # 支持视频、音频、图片、字幕、元数据五种类型
```

#### 3.3 前端API调用
```typescript
export function getTaskFiles(
  taskId: number,
  page: number = 1,
  pageSize: number = 10,
  filters?: {
    fileType?: string;
    search?: string;
    status?: boolean;
  }
)
```

#### 3.4 前端过滤事件处理
```typescript
const handleFileFilterChange = (filters) => {
  currentFileFilters.value = filters;
  fileListPage.value = 1; // 重置到第一页
  fetchTaskFiles(currentTask.value?.id, 1, fileListPageSize.value, filters);
};
```

### 4. 用户体验改进

#### 4.1 视觉优化
- 使用表情符号区分不同文件类型
- 显示每种类型的文件数量
- 加载状态指示器
- 禁用状态处理

#### 4.2 交互优化
- 自动隐藏没有文件的类型选项
- 支持清除过滤条件
- 响应式设计，适配不同屏幕尺寸

#### 4.3 性能优化
- 计算属性缓存，避免重复计算
- 错误边界处理，防止崩溃
- 异步加载系统设置，不阻塞界面

### 5. 兼容性

#### 5.1 向后兼容
- 系统设置未加载时显示所有选项
- 文件类型配置为空时的降级处理
- 保持原有过滤功能不变

#### 5.2 扩展性
- 易于添加新的文件类型
- 支持自定义文件类型配置
- 模块化设计，便于维护

## 使用说明

1. **配置系统设置**：在系统设置页面配置各种文件类型的扩展名
2. **使用过滤器**：在任务管理详情页的文件列表中，使用文件类型下拉框进行过滤
3. **查看统计**：过滤器选项会显示每种类型的文件数量
4. **清除过滤**：点击过滤器的清除按钮或"重置过滤"按钮清除所有过滤条件

## 注意事项

1. 文件类型分类依赖于系统设置，请确保系统设置已正确配置
2. 文件扩展名匹配不区分大小写
3. 如果系统设置加载失败，过滤器会显示为禁用状态
4. 只有包含文件的类型才会在过滤器中显示

## 测试建议

1. 测试不同文件类型的过滤功能
2. 测试系统设置加载失败的情况
3. 测试文件类型配置为空的情况
4. 测试大量文件时的性能表现
5. 测试响应式设计在不同屏幕尺寸下的表现
