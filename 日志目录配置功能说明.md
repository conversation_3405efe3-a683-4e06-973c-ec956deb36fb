# 日志目录配置功能说明

## 功能概述

现在您可以通过前端系统设置页面来配置日志相关设置，包括日志文件的存放目录、日志级别和SQL日志打印等，不再需要修改代码或配置文件。

## 功能特性

### 1. 灵活的路径配置
- **相对路径**：相对于项目根目录，如 `app/logs`、`logs`、`data/logs`
- **绝对路径**：完整的系统路径，如 `/var/log/fast-soy-admin`、`D:\logs`

### 2. 自动目录创建
- 系统会自动创建指定的日志目录
- 如果父目录不存在，会递归创建所有必要的目录

### 3. 权限验证
- 在保存设置时会验证目录的写入权限
- 确保应用能够在指定目录中创建和写入日志文件

### 4. 实时生效
- 日志目录配置变更后立即生效
- 新的日志文件会写入到新配置的目录中

### 5. 日志级别控制
- **DEBUG**：记录所有日志信息，包括详细的调试信息和SQL查询（开发调试用）
- **INFO**：记录一般信息、警告和错误（推荐用于生产环境）
- **WARNING**：仅记录警告和错误信息
- **ERROR**：仅记录错误信息

### 6. SQL日志打印
- 可以启用/禁用数据库SQL查询日志
- 仅在日志级别为DEBUG时可用
- 包含完整的SQL语句和执行参数
- 适用于开发调试和问题排查

## 使用方法

### 前端配置
1. 登录系统管理界面
2. 进入 **系统管理** → **系统设置**
3. 切换到 **日志配置** 选项卡
4. 配置以下选项：
   - **启用SQL日志打印**：是否记录数据库查询日志（需要DEBUG级别）
   - **日志级别**：选择合适的日志记录级别
   - **日志存放目录**：设置日志文件的存储位置
5. 点击 **保存** 按钮

### 配置示例

#### 相对路径示例
- `app/logs` - 默认目录，位于项目根目录下的 app/logs
- `logs` - 位于项目根目录下的 logs 目录
- `data/application/logs` - 位于项目根目录下的 data/application/logs

#### 绝对路径示例
- **Linux/Mac**: `/var/log/fast-soy-admin`、`/home/<USER>/logs`
- **Windows**: `D:\logs`、`C:\ProgramData\FastSoyAdmin\logs`

## 默认配置

- **默认目录**: `app/logs`
- **相对路径基准**: 项目根目录 (`D:\IDE-Code\Cursor\fast-soy-admin`)
- **自动创建**: 是
- **权限检查**: 是

## 注意事项

### 1. SQL日志打印
- **性能影响**：启用SQL日志会记录所有数据库查询，可能影响系统性能
- **日志量大**：会产生大量日志输出，建议仅在调试时使用
- **敏感信息**：SQL日志可能包含敏感数据，请注意安全
- **生产环境**：不建议在生产环境中长期启用
- **依赖条件**：只能在日志级别为DEBUG时启用

### 2. 日志级别选择
- **开发环境**：建议使用DEBUG级别，便于调试
- **测试环境**：建议使用INFO级别，记录关键信息
- **生产环境**：建议使用INFO或WARNING级别，避免过多日志

### 3. 路径格式
- 使用正斜杠 `/` 或反斜杠 `\` 都可以，系统会自动处理
- 避免使用特殊字符和空格

### 4. 权限要求
- 确保应用进程对目标目录有读写权限
- 在 Linux/Mac 系统中，可能需要使用 `chmod` 设置权限

### 5. 磁盘空间
- 确保目标磁盘有足够的空间存储日志文件
- 系统会自动清理 3 天前的日志文件
- SQL日志启用时会产生更多日志文件

### 6. 现有日志文件
- 配置变更不会移动现有的日志文件
- 只有新生成的日志文件会写入到新目录

## 技术实现

### 后端实现
- 在 `SystemSettings` 模型中添加了 `logs_directory` 字段
- 修改了日志系统以支持动态目录配置
- 添加了路径验证和权限检查逻辑

### 前端实现
- 在系统设置页面添加了日志目录配置界面
- 提供了路径格式说明和示例
- 集成了表单验证和错误处理

### 数据库变更
- 新增字段：`logs_directory` (VARCHAR(500), 可为空)
- 默认值：`app/logs`
- 自动迁移：系统启动时自动处理

## 故障排除

### 常见问题

1. **权限错误**
   - 错误信息：`没有权限在目录 'xxx' 中创建或写入文件`
   - 解决方法：检查目录权限，确保应用进程有写入权限

2. **路径无效**
   - 错误信息：`无效的日志目录路径`
   - 解决方法：检查路径格式，确保路径有效且可访问

3. **磁盘空间不足**
   - 现象：日志文件无法创建
   - 解决方法：清理磁盘空间或更换到有足够空间的目录

### 恢复默认设置
如果遇到问题，可以将日志目录设置为 `app/logs` 来恢复默认配置。

## 更新日志

- **2025-07-31**: 初始版本发布
  - 支持通过前端配置日志目录
  - 支持相对路径和绝对路径
  - 添加路径验证和权限检查
  - 实时配置生效
