# 修复前端重复请求问题

## 问题描述
前端存在多个重复请求问题：
1. 点击任务管理中的详情按钮时，会发送两次同样的后端请求 `/api/v1/strm/task/150`
2. 点击任务管理中的日志按钮时，会发送两次同样的后端请求 `/api/v1/strm/task/150/logs`
3. STRM生成页面在任务创建后会立即发送两次状态查询请求

## 问题原因

### 1. 任务详情重复请求
在 `web/src/views/strm/tasks/index.vue` 文件中：
- **第一次请求**：`fetchTaskDetail` 函数中调用 `getTaskStatus(taskId)` 获取任务详情
- **第二次请求**：`fetchTaskFiles` 函数中再次调用 `getTaskStatus(taskId)` 获取文件列表

### 2. 任务日志重复请求
在 `web/src/views/strm/tasks/index.vue` 文件中：
- **第一次请求**：`openLogViewer` 函数调用 `handleLogReset()`，而该函数内部调用 `fetchTaskLogs()`
- **第二次请求**：`openLogViewer` 函数最后直接调用 `fetchTaskLogs()`

### 3. STRM生成页面重复请求
在 `web/src/views/strm/generate/index.vue` 文件中：
- **第一次请求**：任务创建成功后调用 `getTaskStatusOnce()` 获取任务状态
- **第二次请求**：立即调用 `pollTaskStatus()` 开始轮询，而该函数内部也会调用 `getTaskStatus()`

## 解决方案

### 修改 1: 优化任务详情功能 (`fetchTaskDetail`)
- 移除了对 `fetchTaskFiles` 的调用
- 直接在获取任务详情后确保 `files` 字段存在
- 如果后端返回的数据中没有 `files` 字段，则设置为空数组

### 修改 2: 废弃 `fetchTaskFiles` 函数
- 保留函数定义以防其他地方调用
- 移除了实际的 API 请求逻辑
- 添加了说明注释

### 修改 3: 优化任务日志功能 (`openLogViewer`)
- 移除了对 `handleLogReset()` 的调用
- 直接在函数内部重置过滤器和统计信息
- 只在最后调用一次 `fetchTaskLogs()`

### 修改 4: 优化STRM生成页面轮询逻辑
- 在任务创建成功后，先调用 `getTaskStatusOnce()` 获取状态
- 延迟5秒后再开始轮询，避免立即发送第二次请求
- 只有在任务仍在运行时才启动轮询

## 修改的代码位置

### 文件：`web/src/views/strm/tasks/index.vue`

#### 修改前（第502-555行）：
```javascript
// 获取任务详情
const fetchTaskDetail = async (taskId: number) => {
  // ... 获取任务详情
  
  // 如果任务对象中已包含files字段，无需再次请求
  if (currentTask.value.files && Array.isArray(currentTask.value.files) && currentTask.value.files.length > 0) {
    console.log('任务详情中已包含文件列表，跳过单独请求');
  } else {
    // 否则请求文件列表 - 这里会发送第二次请求
    await fetchTaskFiles(taskId);
  }
};
```

#### 修改后：
```javascript
// 获取任务详情
const fetchTaskDetail = async (taskId: number) => {
  // ... 获取任务详情
  
  // 确保文件列表字段存在，如果不存在则设置为空数组
  if (!currentTask.value.files || !Array.isArray(currentTask.value.files)) {
    currentTask.value.files = [];
  }
  
  showTaskDetailModal.value = true;
  console.log('任务详情获取完成，文件列表数量:', currentTask.value.files.length);
};
```

## 预期效果
- 点击任务详情按钮时，只会发送一次 `/api/v1/strm/task/{id}` 请求
- 减少了不必要的网络请求，提升了页面响应速度
- 保持了原有的功能不变

## 修复验证
已经完成了以下修改：

1. ✅ **优化了任务详情功能**
   - 修改了 `fetchTaskDetail` 函数，移除对 `fetchTaskFiles` 的调用
   - 直接确保 `files` 字段存在，避免重复请求
   - 废弃了 `fetchTaskFiles` 函数的实际请求逻辑

2. ✅ **优化了任务日志功能**
   - 修改了 `openLogViewer` 函数，移除对 `handleLogReset()` 的调用
   - 直接在函数内部重置过滤器，避免重复请求

3. ✅ **优化了STRM生成页面轮询逻辑**
   - 修改了任务创建成功后的状态查询逻辑
   - 添加了5秒延迟，避免立即发送第二次请求

4. ✅ **全面检查了其他可能的重复请求源**
   - 确认 `getTaskStatus` 在其他地方的调用都是正常的（如轮询任务状态）
   - 确认没有 watch 或生命周期钩子导致重复请求
   - 检查了所有API调用模式，确保没有其他重复请求问题

## 测试建议

### 任务详情功能测试
1. 打开浏览器开发者工具的网络面板
2. 点击任务管理页面中的"详情"按钮
3. 确认只有一次 `/api/v1/strm/task/{id}` 请求
4. 验证任务详情弹窗正常显示
5. 验证文件列表标签页正常工作

### 任务日志功能测试
1. 打开浏览器开发者工具的网络面板
2. 点击任务管理页面中的"日志"按钮
3. 确认只有一次 `/api/v1/strm/task/{id}/logs` 请求
4. 验证日志弹窗正常显示
5. 验证日志内容正确加载

### STRM生成页面测试
1. 打开浏览器开发者工具的网络面板
2. 创建一个新的STRM任务
3. 确认任务创建成功后只有一次立即的状态查询请求
4. 确认5秒后开始正常的轮询请求
5. 验证任务状态正常更新

## 技术细节

### 修改文件和行数
- **`web/src/views/strm/tasks/index.vue`**: 第502-560行（任务详情）、第1255-1279行（任务日志）
- **`web/src/views/strm/generate/index.vue`**: 第815-823行（STRM生成轮询）

### 影响范围
- 仅影响任务详情查看、日志查看和STRM生成功能的请求逻辑
- 不影响其他功能的正常使用
- 所有修改都是向后兼容的

### 性能提升
- 减少了50%的不必要网络请求
- 提升了页面响应速度
- 降低了服务器负载
