#!/usr/bin/env python
"""
测试STRM任务列表接口的性能
"""

import asyncio
import time
import sys
from tortoise import Tortoise
from app.settings.config import settings
from app.models.system import User
from app.controllers.strm.task_controller import get_user_tasks


async def test_performance():
    """测试性能"""
    print("开始性能测试...")

    # 连接到数据库
    await Tortoise.init(
        config=settings.TORTOISE_ORM
    )

    try:
        # 获取一个测试用户
        user = await User.first()
        if not user:
            print("未找到测试用户，请先创建用户")
            return

        print(f"使用用户: {user.user_name} (ID: {user.id})")

        # 测试多次请求的平均响应时间
        test_rounds = 5
        total_time = 0
        
        for i in range(test_rounds):
            start_time = time.time()
            
            result = await get_user_tasks(
                user=user,
                page=1,
                page_size=10
            )
            
            end_time = time.time()
            duration = (end_time - start_time) * 1000  # 转换为毫秒
            
            print(f"第 {i+1} 次请求: {duration:.2f}ms, 返回 {len(result['tasks'])} 个任务")
            total_time += duration

        avg_time = total_time / test_rounds
        print(f"\n平均响应时间: {avg_time:.2f}ms")
        
        if avg_time > 100:
            print("⚠️  响应时间超过100ms，建议进一步优化")
        elif avg_time > 50:
            print("⚡ 响应时间良好，但仍有优化空间")
        else:
            print("✅ 响应时间优秀！")

    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    try:
        asyncio.run(test_performance())
        sys.exit(0)
    except Exception as e:
        print(f"性能测试失败: {str(e)}")
        sys.exit(1)
