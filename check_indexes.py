#!/usr/bin/env python3
"""
检查数据库中索引状态的脚本
用于验证索引删除结果和应用启动后的索引创建情况
"""

import sqlite3
import os
import sys
from pathlib import Path


def get_database_path():
    """获取数据库文件路径"""
    # 从.env文件中读取数据库路径
    env_file = Path(".env")
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip().startswith('TORTOISE_ORM='):
                    # 解析TORTOISE_ORM配置中的file_path
                    if 'app_system.sqlite3' in line:
                        return "app_system.sqlite3"
    
    # 默认路径
    return "app_system.sqlite3"


def connect_to_database(db_path):
    """连接到数据库"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return None
    
    try:
        conn = sqlite3.connect(db_path)
        print(f"✅ 成功连接到数据库: {db_path}")
        return conn
    except Exception as e:
        print(f"❌ 连接数据库失败: {e}")
        return None


def get_all_indexes(conn):
    """获取数据库中所有的索引"""
    cursor = conn.cursor()
    
    # 查询所有索引（排除主键索引和自动创建的索引）
    query = """
    SELECT name, tbl_name, sql 
    FROM sqlite_master 
    WHERE type = 'index' 
    AND name NOT LIKE 'sqlite_%'
    AND name NOT LIKE '%_pk'
    AND sql IS NOT NULL
    ORDER BY tbl_name, name
    """
    
    cursor.execute(query)
    indexes = cursor.fetchall()
    
    return indexes


def get_all_tables(conn):
    """获取数据库中所有的表"""
    cursor = conn.cursor()
    
    query = """
    SELECT name 
    FROM sqlite_master 
    WHERE type = 'table' 
    AND name NOT LIKE 'sqlite_%'
    AND name NOT LIKE 'aerich%'
    ORDER BY name
    """
    
    cursor.execute(query)
    tables = cursor.fetchall()
    
    return [table[0] for table in tables]


def get_table_info(conn, table_name):
    """获取表的基本信息"""
    cursor = conn.cursor()
    
    try:
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        # 获取表的记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        return len(columns), count
    except Exception as e:
        return 0, 0


def main():
    """主函数"""
    print("🔍 检查数据库索引状态...")
    print("=" * 60)
    
    # 获取数据库路径
    db_path = get_database_path()
    print(f"📍 数据库路径: {db_path}")
    
    # 连接数据库
    conn = connect_to_database(db_path)
    if not conn:
        sys.exit(1)
    
    try:
        # 获取所有索引
        print("\n🔍 查询数据库中的所有索引...")
        indexes = get_all_indexes(conn)
        
        print(f"📊 当前数据库中共有 {len(indexes)} 个索引")
        
        if indexes:
            print("\n📋 索引列表:")
            print("-" * 60)
            for index_name, table_name, sql in indexes:
                print(f"📋 表: {table_name:<25} 索引: {index_name}")
        else:
            print("✅ 数据库中没有找到任何索引（除了主键索引）")
        
        # 获取所有表的信息
        print(f"\n📊 数据库表统计:")
        print("-" * 60)
        tables = get_all_tables(conn)
        
        for table in tables:
            col_count, row_count = get_table_info(conn, table)
            print(f"📋 表: {table:<25} 列数: {col_count:<3} 记录数: {row_count}")
        
        print("-" * 60)
        print(f"📈 总计: {len(tables)} 个表")
        
        # 总结
        print(f"\n📋 检查结果总结:")
        print(f"🗄️ 数据库文件: {db_path}")
        print(f"📊 表数量: {len(tables)}")
        print(f"🔍 索引数量: {len(indexes)}")
        
        if len(indexes) == 0:
            print(f"✅ 所有索引已成功删除！")
            print(f"🚀 现在可以启动应用测试索引自动创建功能")
        else:
            print(f"⚠️ 仍有 {len(indexes)} 个索引存在")
        
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        sys.exit(1)
    
    finally:
        conn.close()
        print("🔒 数据库连接已关闭")


if __name__ == "__main__":
    main()
