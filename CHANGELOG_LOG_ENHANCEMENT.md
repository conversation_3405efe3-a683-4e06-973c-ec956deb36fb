# 下载日志和STRM生成日志数据库存储功能实现

## 概述
本次更新为系统添加了详细的下载日志和STRM生成日志记录功能，将这些日志存储到数据库中，并在前端任务管理页面中提供更丰富的日志查看体验。

## 主要修改

### 1. 数据库模型 (app/models/strm/download.py)
- **新增 `DownloadLog` 模型**: 记录文件下载的详细日志
  - 包含文件路径、目标路径、文件类型、文件大小、下载时间、下载速度等信息
  - 支持成功/失败状态记录和错误信息存储
  
- **新增 `StrmLog` 模型**: 记录STRM文件生成的详细日志
  - 包含源文件路径、目标STRM文件路径、生成时间等信息
  - 支持成功/失败状态记录和错误信息存储

### 2. 处理器增强 (app/utils/strm/processor.py)
- **下载功能增强**: 
  - 在 `download_worker` 方法中添加详细的日志记录
  - 记录下载开始、成功、失败等各个阶段的信息
  - 计算并记录下载时间和下载速度

- **STRM生成功能增强**:
  - 在 `createstrm` 方法中添加详细的日志记录
  - 记录STRM文件生成的各个阶段
  - 计算并记录生成时间

- **新增日志记录方法**:
  - `log_download()`: 专门用于记录下载日志到数据库
  - `log_strm()`: 专门用于记录STRM生成日志到数据库

### 3. 控制器更新 (app/controllers/strm/task_controller.py)
- **增强 `get_task_logs` 函数**:
  - 添加 `log_type` 参数支持日志类型筛选
  - 支持获取任务日志、下载日志、STRM生成日志
  - 统一时间排序，提供更好的日志查看体验
  - 返回详细的日志信息供前端使用

### 4. API接口更新 (app/api/v1/strm.py)
- **更新日志API接口**:
  - 添加 `log_type` 查询参数
  - 支持按日志类型筛选 (task/download/strm)
  - 完善API文档说明

### 5. 前端功能增强 (web/src/views/strm/tasks/index.vue)
- **日志查看界面优化**:
  - 添加日志类型筛选下拉框
  - 支持按任务日志、下载日志、STRM生成日志分类查看
  - 保持原有的日志级别和搜索功能

### 6. 前端API服务更新 (web/src/service/api/strm.ts)
- **更新 `getTaskLogs` 函数**:
  - 添加 `log_type` 参数支持
  - 完善TypeScript类型定义

## 数据库表结构

### strm_download_logs 表
- `id`: 主键
- `task_id`: 关联的任务ID
- `file_path`: 文件路径
- `target_path`: 目标路径
- `file_type`: 文件类型
- `file_size`: 文件大小（字节）
- `download_time`: 下载耗时（秒）
- `download_speed`: 下载速度（字节/秒）
- `is_success`: 是否成功
- `log_level`: 日志级别
- `log_message`: 日志消息
- `error_message`: 错误信息
- `create_time`: 创建时间
- `update_time`: 更新时间

### strm_generation_logs 表
- `id`: 主键
- `task_id`: 关联的任务ID
- `source_path`: 源文件路径
- `target_path`: 生成的STRM文件路径
- `file_type`: 文件类型
- `is_success`: 是否成功
- `log_level`: 日志级别
- `log_message`: 日志消息
- `error_message`: 错误信息
- `generation_time`: 生成耗时（秒）
- `create_time`: 创建时间
- `update_time`: 更新时间

## 功能特性

1. **详细日志记录**: 记录下载和STRM生成过程中的每个步骤
2. **性能监控**: 记录下载速度、处理时间等性能指标
3. **错误追踪**: 详细记录错误信息，便于问题排查
4. **分类查看**: 支持按日志类型分类查看，提高用户体验
5. **搜索过滤**: 支持按关键词、日志级别、日志类型进行筛选
6. **数据持久化**: 所有日志数据存储在数据库中，不会丢失

## 使用说明

1. **查看所有日志**: 在任务管理页面点击"日志"按钮，默认显示所有类型的日志
2. **筛选日志类型**: 使用"日志类型"下拉框选择要查看的日志类型
3. **搜索日志**: 使用搜索框输入关键词进行日志内容搜索
4. **级别筛选**: 使用"日志级别"下拉框筛选特定级别的日志
5. **导出日志**: 点击"导出日志"按钮可以将当前筛选的日志导出为文本文件

## 注意事项

1. 数据库迁移会自动创建新的日志表
2. 现有任务的历史日志仍然保存在任务的 `log_content` 字段中
3. 新的日志记录会同时写入数据库和任务的 `log_content` 字段
4. 日志查看界面会合并显示所有类型的日志，按时间排序
