# STRM生成页面状态持久化测试

## 修改内容总结

### 1. 新增功能
- **URL参数状态管理**：在URL中保存当前步骤和任务ID
- **本地存储备份**：将关键状态保存到localStorage
- **页面刷新恢复**：页面刷新后自动恢复到正确状态
- **状态自动同步**：状态变化时自动保存到URL和本地存储

### 2. 主要修改点

#### 2.1 导入新的Vue组合式API
```javascript
import { ref, onMounted, onUnmounted, onBeforeUnmount, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
```

#### 2.2 新增状态管理函数
- `saveCurrentState()`: 保存当前状态到本地存储和URL
- `restoreState()`: 从URL和本地存储恢复状态

#### 2.3 状态监听
- 监听关键状态变化，自动保存状态
- 页面卸载时保存状态

#### 2.4 UI改进
- 在第三步添加"重新开始"按钮，方便用户重置状态

### 3. 测试场景

#### 场景1：正常流程测试
1. 进入STRM生成页面（第一步）
2. 选择文件，点击"下一步"（第二步）
3. 配置服务器，点击"开始生成"（第三步）
4. 观察URL是否包含step=2和taskId参数

#### 场景2：页面刷新测试
1. 在第三步生成进度页面
2. 刷新浏览器页面
3. 验证页面是否自动恢复到第三步
4. 验证任务状态是否正确显示

#### 场景3：URL直接访问测试
1. 复制包含step和taskId参数的URL
2. 在新标签页中打开该URL
3. 验证页面是否直接显示对应步骤和任务状态

#### 场景4：重置功能测试
1. 在任意步骤点击"重新开始"按钮
2. 验证页面是否回到第一步
3. 验证URL参数是否被清除
4. 验证本地存储是否被清除

### 4. 预期效果

- ✅ 页面刷新后不再回到第一步
- ✅ URL包含当前状态信息，支持直接访问
- ✅ 本地存储作为备份，确保状态不丢失
- ✅ 状态变化时自动同步到URL和存储
- ✅ 提供重置功能，方便用户重新开始

### 5. 技术实现细节

#### 5.1 状态保存结构
```javascript
{
  step: number,           // 当前步骤 (0, 1, 2)
  taskId: number,         // 任务ID
  fileId: number,         // 选择的文件ID
  serverId: number,       // 媒体服务器ID
  downloadServerId: number // 下载服务器ID
}
```

#### 5.2 URL参数格式
```
/strm/generate?step=2&taskId=123
```

#### 5.3 本地存储键名
```javascript
const STRM_GENERATE_STATE_KEY = 'strm_generate_state';
```

### 6. 错误处理

- localStorage操作使用try-catch包装
- URL参数解析失败时回退到本地存储
- 本地存储恢复失败时正常初始化页面
- 任务状态获取失败时显示错误信息

### 7. 兼容性考虑

- 保持向后兼容，不影响现有功能
- 优雅降级，即使状态恢复失败也能正常使用
- 清理机制，避免存储过期数据
