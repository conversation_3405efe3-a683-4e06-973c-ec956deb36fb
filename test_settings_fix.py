#!/usr/bin/env python3
"""
测试系统设置修复的脚本
"""
import asyncio
import json
from app.controllers.strm.system_controller import system_settings_controller

async def test_settings_fix():
    """测试系统设置的获取和保存功能"""
    print("🔍 开始测试系统设置修复...")
    
    # 1. 获取当前设置
    print("\n1. 获取当前系统设置:")
    current_settings = await system_settings_controller.get_settings()
    if current_settings:
        print(f"   失败重试次数: {current_settings.get('failure_retry_count', '未找到')}")
        print(f"   重试间隔时间: {current_settings.get('retry_interval_seconds', '未找到')}")
        print(f"   设置版本: {current_settings.get('settings_version', '未找到')}")
    else:
        print("   ❌ 未找到系统设置")
        return
    
    # 2. 测试保存新的设置值
    print("\n2. 测试保存新的设置值:")
    test_data = {
        "failure_retry_count": 2,
        "retry_interval_seconds": 20
    }
    print(f"   设置失败重试次数为: {test_data['failure_retry_count']}")
    print(f"   设置重试间隔时间为: {test_data['retry_interval_seconds']}")
    
    try:
        updated_settings = await system_settings_controller.create_or_update_settings(test_data)
        print("   ✅ 设置保存成功")
        print(f"   保存后失败重试次数: {updated_settings.get('failure_retry_count', '未找到')}")
        print(f"   保存后重试间隔时间: {updated_settings.get('retry_interval_seconds', '未找到')}")
    except Exception as e:
        print(f"   ❌ 设置保存失败: {str(e)}")
        return
    
    # 3. 重新获取设置验证是否正确保存
    print("\n3. 重新获取设置验证:")
    refreshed_settings = await system_settings_controller.get_settings()
    if refreshed_settings:
        retry_count = refreshed_settings.get('failure_retry_count')
        retry_interval = refreshed_settings.get('retry_interval_seconds')
        
        print(f"   重新获取的失败重试次数: {retry_count}")
        print(f"   重新获取的重试间隔时间: {retry_interval}")
        
        # 验证值是否正确
        if retry_count == 2 and retry_interval == 20:
            print("   ✅ 测试通过！设置值正确保存和获取")
        else:
            print("   ❌ 测试失败！设置值不匹配")
            print(f"   期望: 重试次数=2, 重试间隔=20")
            print(f"   实际: 重试次数={retry_count}, 重试间隔={retry_interval}")
    else:
        print("   ❌ 重新获取设置失败")
    
    # 4. 恢复原始设置
    print("\n4. 恢复原始设置:")
    original_data = {
        "failure_retry_count": current_settings.get('failure_retry_count', 3),
        "retry_interval_seconds": current_settings.get('retry_interval_seconds', 30)
    }
    
    try:
        await system_settings_controller.create_or_update_settings(original_data)
        print("   ✅ 原始设置已恢复")
    except Exception as e:
        print(f"   ❌ 恢复原始设置失败: {str(e)}")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(test_settings_fix())
