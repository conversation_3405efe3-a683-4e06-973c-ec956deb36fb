#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加STRM生成菜单项的脚本
用法: python update_strm_menu.py
"""

import asyncio
import os
import sys
from pathlib import Path

# 将项目根目录添加到系统路径
ROOT_DIR = Path(__file__).parent.absolute()
sys.path.append(str(ROOT_DIR))

# 导入需要的模型和依赖
from app.models.system import Menu, StatusType, MenuType, IconType
from tortoise import Tortoise
from app.settings import APP_SETTINGS


async def init_tortoise():
    """初始化Tortoise ORM"""
    await Tortoise.init(config=APP_SETTINGS.TORTOISE_ORM)
    print("数据库连接已初始化")


async def close_tortoise():
    """关闭Tortoise ORM连接"""
    await Tortoise.close_connections()
    print("数据库连接已关闭")


async def add_strm_generate_menu():
    """添加STRM生成菜单项"""
    try:
        # 查找STRM根菜单
        strm_root_menu = await Menu.filter(route_name="strm").first()
        if not strm_root_menu:
            print("错误：未找到STRM根菜单，请先确保STRM根菜单已存在")
            return False

        # 检查STRM生成菜单是否已存在
        existing_menu = await Menu.filter(route_name="strm_generate").first()
        if existing_menu:
            print("STRM生成菜单已存在，无需添加")
            return True

        # 为了保持顺序，将原有的strm_history菜单的顺序更新为3
        history_menu = await Menu.filter(route_name="strm_history").first()
        if history_menu:
            history_menu.order = 3
            await history_menu.save()
            print("已将history菜单的顺序更新为3")

        # 创建STRM生成菜单
        generate_menu = await Menu.create(
            status_type=StatusType.enable,
            parent_id=strm_root_menu.id,
            menu_type=MenuType.menu,
            menu_name="文件生成",
            route_name="strm_generate",
            route_path="/strm/generate",
            component="view.strm_generate",
            order=2,
            i18n_key="route.strm_generate",
            icon="material-symbols:file-copy-outline",
            icon_type=IconType.iconify,
        )

        print(f"STRM生成菜单已成功添加，ID: {generate_menu.id}")
        return True

    except Exception as e:
        print(f"添加菜单时发生错误: {e}")
        return False


async def main():
    """主函数"""
    print("开始执行STRM生成菜单添加脚本...")

    try:
        await init_tortoise()
        success = await add_strm_generate_menu()
        if success:
            print("STRM生成菜单添加操作已完成")
        else:
            print("STRM生成菜单添加操作失败")
    finally:
        await close_tortoise()


if __name__ == "__main__":
    asyncio.run(main())
