#!/usr/bin/env python
"""
执行Tortoise ORM数据库迁移并生成下载任务表
"""

import asyncio
import os
import sys

from tortoise import Tortoise
from app.settings.config import DATABASE
from app.models.strm import DownloadTask


async def run_migrations():
    """执行数据库迁移"""
    print("开始执行数据库迁移...")

    # 连接到数据库
    await Tortoise.init(
        db_url=DATABASE["conn_system"],
        modules={"app_system": ["app.models.system", "app.models.strm"]},
    )

    # 生成迁移文件并执行
    print("生成并应用迁移...")
    await Tortoise.generate_schemas()

    # 检查DownloadTask表是否存在
    conn = Tortoise.get_connection("conn_system")
    result = await conn.execute_query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='strm_download_tasks'"
    )
    if result[1]:
        print("成功创建下载任务表 (strm_download_tasks)")
    else:
        print("警告：下载任务表可能未成功创建，请检查错误信息")

    # 关闭连接
    await Tortoise.close_connections()
    print("数据库迁移完成!")


if __name__ == "__main__":
    try:
        asyncio.run(run_migrations())
        sys.exit(0)
    except Exception as e:
        print(f"迁移失败: {str(e)}")
        sys.exit(1)
