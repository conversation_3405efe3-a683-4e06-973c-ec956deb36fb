#!/usr/bin/env python
"""
验证模型中定义的索引是否正确创建
"""

import asyncio
import sys
from tortoise import Tortoise
from app.settings.config import settings


async def verify_model_indexes():
    """验证模型索引"""
    print("🔍 开始验证模型索引...")

    # 连接到数据库
    await Tortoise.init(
        config=settings.TORTOISE_ORM
    )

    try:
        conn = Tortoise.get_connection("conn_system")
        
        # 获取所有索引
        result = await conn.execute_query(
            "SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%' ORDER BY tbl_name, name"
        )
        
        if not result[1]:
            print("❌ 未找到任何索引")
            return
        
        # 按表分组显示索引
        indexes_by_table = {}
        for row in result[1]:
            index_name, table_name, sql = row
            if table_name not in indexes_by_table:
                indexes_by_table[table_name] = []
            indexes_by_table[table_name].append({
                'name': index_name,
                'sql': sql
            })
        
        print(f"📊 找到 {len(result[1])} 个索引，按表分组如下：\n")
        
        # 重点关注STRM相关表的索引
        strm_tables = ['strm_tasks', 'strm_download_tasks']
        
        for table_name in sorted(indexes_by_table.keys()):
            indexes = indexes_by_table[table_name]
            
            if table_name in strm_tables:
                print(f"🎯 {table_name} ({len(indexes)} 个索引):")
            else:
                print(f"📋 {table_name} ({len(indexes)} 个索引):")
            
            for index in indexes:
                if table_name in strm_tables:
                    print(f"  ✅ {index['name']}")
                else:
                    print(f"  - {index['name']}")
            print()
        
        # 验证关键性能索引
        print("🚀 验证关键性能索引:")
        
        critical_indexes = [
            'strm_tasks',
            'strm_download_tasks'
        ]
        
        for table in critical_indexes:
            if table in indexes_by_table:
                table_indexes = [idx['name'] for idx in indexes_by_table[table]]
                
                # 检查是否包含我们定义的索引模式
                has_task_id = any('task_id' in name for name in table_indexes)
                has_status = any('status' in name for name in table_indexes)
                has_created_by = any('created' in name for name in table_indexes)
                
                if table == 'strm_download_tasks':
                    if has_task_id and has_status:
                        print(f"  ✅ {table}: 关键索引已创建")
                    else:
                        print(f"  ⚠️ {table}: 缺少关键索引")
                elif table == 'strm_tasks':
                    if has_created_by and has_status:
                        print(f"  ✅ {table}: 关键索引已创建")
                    else:
                        print(f"  ⚠️ {table}: 缺少关键索引")
            else:
                print(f"  ❌ {table}: 表不存在")
        
        print(f"\n✅ 索引验证完成！")
        
    except Exception as e:
        print(f"❌ 验证索引失败: {str(e)}")
        raise
    finally:
        # 关闭连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    try:
        asyncio.run(verify_model_indexes())
        sys.exit(0)
    except Exception as e:
        print(f"验证失败: {str(e)}")
        sys.exit(1)
