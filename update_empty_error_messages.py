#!/usr/bin/env python3
"""
更新空的错误信息脚本

此脚本用于更新数据库中状态为失败或取消但error_message为空的下载任务记录，
为它们设置合适的默认错误信息。
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from app.models.strm.download import DownloadTask, DownloadTaskStatus
from app.core.config import settings


async def update_empty_error_messages():
    """更新空的错误信息"""
    
    # 初始化数据库连接
    await Tortoise.init(
        db_url=settings.DATABASE_URL,
        modules={"models": ["app.models"]}
    )
    
    try:
        print("🔍 查找需要更新的记录...")
        
        # 查找状态为失败但error_message为空的记录
        failed_tasks = await DownloadTask.filter(
            status=DownloadTaskStatus.FAILED,
            error_message__in=["", None]
        ).all()
        
        # 查找状态为取消但error_message为空的记录
        canceled_tasks = await DownloadTask.filter(
            status=DownloadTaskStatus.CANCELED,
            error_message__in=["", None]
        ).all()
        
        print(f"📊 找到 {len(failed_tasks)} 个失败任务需要更新错误信息")
        print(f"📊 找到 {len(canceled_tasks)} 个取消任务需要更新错误信息")
        
        # 更新失败任务的错误信息
        updated_failed = 0
        for task in failed_tasks:
            task.error_message = "文件处理失败，但未记录具体错误信息"
            await task.save(update_fields=["error_message"])
            updated_failed += 1
            
            if updated_failed % 100 == 0:
                print(f"⏳ 已更新 {updated_failed}/{len(failed_tasks)} 个失败任务...")
        
        # 更新取消任务的错误信息
        updated_canceled = 0
        for task in canceled_tasks:
            task.error_message = "任务已被取消"
            await task.save(update_fields=["error_message"])
            updated_canceled += 1
            
            if updated_canceled % 100 == 0:
                print(f"⏳ 已更新 {updated_canceled}/{len(canceled_tasks)} 个取消任务...")
        
        print(f"✅ 更新完成！")
        print(f"   - 失败任务: {updated_failed} 个")
        print(f"   - 取消任务: {updated_canceled} 个")
        print(f"   - 总计: {updated_failed + updated_canceled} 个")
        
    except Exception as e:
        print(f"❌ 更新过程中发生错误: {str(e)}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


async def verify_updates():
    """验证更新结果"""
    
    # 初始化数据库连接
    await Tortoise.init(
        db_url=settings.DATABASE_URL,
        modules={"models": ["app.models"]}
    )
    
    try:
        print("\n🔍 验证更新结果...")
        
        # 检查是否还有空的错误信息
        failed_empty = await DownloadTask.filter(
            status=DownloadTaskStatus.FAILED,
            error_message__in=["", None]
        ).count()
        
        canceled_empty = await DownloadTask.filter(
            status=DownloadTaskStatus.CANCELED,
            error_message__in=["", None]
        ).count()
        
        if failed_empty == 0 and canceled_empty == 0:
            print("✅ 验证通过！所有失败和取消的任务都有错误信息")
        else:
            print(f"⚠️ 仍有记录需要处理:")
            print(f"   - 失败任务空错误信息: {failed_empty} 个")
            print(f"   - 取消任务空错误信息: {canceled_empty} 个")
        
        # 显示统计信息
        total_failed = await DownloadTask.filter(status=DownloadTaskStatus.FAILED).count()
        total_canceled = await DownloadTask.filter(status=DownloadTaskStatus.CANCELED).count()
        
        print(f"\n📊 当前统计:")
        print(f"   - 总失败任务: {total_failed} 个")
        print(f"   - 总取消任务: {total_canceled} 个")
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {str(e)}")
        raise
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


async def main():
    """主函数"""
    print("🚀 开始更新空的错误信息...")
    
    try:
        await update_empty_error_messages()
        await verify_updates()
        print("\n🎉 所有操作完成！")
    except Exception as e:
        print(f"\n💥 操作失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
