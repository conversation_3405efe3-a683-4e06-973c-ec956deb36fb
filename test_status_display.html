<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .status-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        .status-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-success {
            background-color: rgba(82, 196, 26, 0.1);
            color: #52c41a;
            border: 1px solid rgba(82, 196, 26, 0.3);
        }
        .status-error {
            background-color: rgba(245, 34, 45, 0.1);
            color: #ff4d4f;
            border: 1px solid rgba(245, 34, 45, 0.3);
        }
        .status-warning {
            background-color: rgba(250, 140, 22, 0.1);
            color: #fa8c16;
            border: 1px solid rgba(250, 140, 22, 0.3);
        }
        .status-info {
            background-color: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            border: 1px solid rgba(24, 144, 255, 0.3);
        }
        .status-default {
            background-color: rgba(140, 140, 140, 0.1);
            color: #8c8c8c;
            border: 1px solid rgba(140, 140, 140, 0.3);
        }
        .file-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 10px;
            border-left: 4px solid #d9d9d9;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .file-card.success {
            border-left-color: #52c41a;
        }
        .file-card.error {
            border-left-color: #ff4d4f;
        }
        .file-card.canceled {
            border-left-color: #fa8c16;
        }
        .file-card.processing {
            border-left-color: #1890ff;
        }
        .file-card.pending {
            border-left-color: #8c8c8c;
        }
        h2 {
            color: #333;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <h1>任务文件状态显示测试</h1>
    
    <h2>状态标签样式</h2>
    <div class="status-container">
        <span class="status-tag status-success">✅ 成功</span>
        <span class="status-tag status-error">❌ 失败</span>
        <span class="status-tag status-warning">🚫 已取消</span>
        <span class="status-tag status-info">⏳ 处理中</span>
        <span class="status-tag status-default">⏸️ 等待中</span>
    </div>

    <h2>文件卡片样式</h2>
    <div class="file-card success">
        <div><strong>文件名:</strong> video.mp4</div>
        <div><strong>状态:</strong> <span class="status-tag status-success">✅ 成功</span></div>
        <div><strong>描述:</strong> 文件处理成功，可以预览</div>
    </div>

    <div class="file-card error">
        <div><strong>文件名:</strong> corrupted.mp4</div>
        <div><strong>状态:</strong> <span class="status-tag status-error">❌ 失败</span></div>
        <div><strong>描述:</strong> 文件处理失败，出现错误</div>
    </div>

    <div class="file-card canceled">
        <div><strong>文件名:</strong> large_file.mkv</div>
        <div><strong>状态:</strong> <span class="status-tag status-warning">🚫 已取消</span></div>
        <div><strong>描述:</strong> 任务被用户取消，文件未完成处理</div>
    </div>

    <div class="file-card processing">
        <div><strong>文件名:</strong> downloading.mp4</div>
        <div><strong>状态:</strong> <span class="status-tag status-info">⏳ 处理中</span></div>
        <div><strong>描述:</strong> 文件正在处理中</div>
    </div>

    <div class="file-card pending">
        <div><strong>文件名:</strong> queued.mp4</div>
        <div><strong>状态:</strong> <span class="status-tag status-default">⏸️ 等待中</span></div>
        <div><strong>描述:</strong> 文件在队列中等待处理</div>
    </div>

    <h2>状态说明</h2>
    <ul>
        <li><strong>✅ 成功 (completed):</strong> 文件已成功处理，可以预览和下载</li>
        <li><strong>❌ 失败 (failed):</strong> 文件处理过程中出现错误，无法完成</li>
        <li><strong>🚫 已取消 (canceled):</strong> 任务被用户主动取消，文件未完成处理</li>
        <li><strong>⏳ 处理中 (downloading):</strong> 文件正在处理中，请等待</li>
        <li><strong>⏸️ 等待中 (pending):</strong> 文件在队列中等待处理</li>
    </ul>
</body>
</html>
